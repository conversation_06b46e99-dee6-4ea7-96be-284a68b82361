using System.Text.RegularExpressions;

using App.Base.Repository;
using App.Base.Utilities;
using App.ECommerce.ProcessFlow;
using App.ECommerce.Repository.Entities;
using App.ECommerce.Repository.Interface;
using App.ECommerce.Resource.Dtos.InputDtos;
using App.ECommerce.Resource.Dtos.ResultDtos;
using App.ECommerce.Resource.Model;
using App.ECommerce.Units;
using App.ECommerce.Units.Consts;
using App.ECommerce.Units.Enums;
using App.ECommerce.Units.Enums.Order;

using log4net;

using MongoDB.Bson;
using MongoDB.Driver;

using Newtonsoft.Json;

using OfficeOpenXml;
using OfficeOpenXml.Style;

using static App.ECommerce.Controllers.API.OrderPartnerController;

using Order = App.ECommerce.Repository.Entities.Order;

namespace App.ECommerce.Repository.Implement;

public class OrderRepository : BaseRepository, IOrderRepository
{
    private readonly ILog _log = log4net.LogManager.GetLogger(typeof(OrderRepository));

    private readonly IMongoCollection<Order> _collectionOrder;

    public OrderRepository() : base()
    {
        _collectionOrder = _database.GetCollection<Order>($"Order");

        var indexOptions = new CreateIndexOptions();
        var indexModelOrder = new List<CreateIndexModel<Order>>()
        {
            new CreateIndexModel<Order>(Builders<Order>.IndexKeys.Ascending(item => item.OrderId), indexOptions),
            new CreateIndexModel<Order>(Builders<Order>.IndexKeys.Ascending(item => item.TransactionId), indexOptions),
            new CreateIndexModel<Order>(Builders<Order>.IndexKeys.Ascending(item => item.PartnerId), indexOptions),
        };
        _collectionOrder.Indexes.CreateMany(indexModelOrder);
    }

    public async Task<Order> CreateOrder(Order item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.OrderId = Guid.NewGuid().ToString();
        item.OrderNo = Id64.Generator();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();

        await _collectionOrder.InsertOneAsync(item);

        return item;
    }

    public Order RestoreOrder(Order item)
    {
        ObjectId objectId = ObjectId.GenerateNewId();
        item.Id = new BsonObjectId(objectId).ToString();
        item.OrderId = (!string.IsNullOrEmpty(item.OrderId) ? item.OrderId : Guid.NewGuid().ToString());
        item.OrderNo = Id64.Generator();
        item.Created = DateTimes.Now();
        item.Updated = DateTimes.Now();
        _collectionOrder.InsertOne(item);
        return item;
    }

    public Order DeleteOrder(string orderId)
    {
        return _collectionOrder.FindOneAndDelete(item => item.OrderId == orderId);
    }

    public bool DeleteSameTransactionId(string transactionId, string userId)
    {
        _collectionOrder.DeleteMany(x => x.TransactionId == transactionId && x.UserShippingAddress.UserId == userId && x.StatusOrder != TypeOrderStatus.Success);
        return true;
    }
    public Order? FindByOrderId(string orderId)
    {
        return _collectionOrder.Find(item => item.OrderId == orderId).FirstOrDefault();
    }

    public Order? FindByOrderNo(string orderNo)
    {
        return _collectionOrder.Find(item => item.OrderNo == orderNo).FirstOrDefault();
    }

    public Order? FindByExternalId(string shopId, string externalId, SyncServiceEnum externalSource)
    {
        return _collectionOrder.Find(item => item.ExternalId == externalId &&
        item.ExternalSource == externalSource && item.ShopId == shopId).FirstOrDefault();
    }

    public Order? FindByTransportOrderId(string transportOrderId, TypeTransportService typeTransportService)
    {
        return _collectionOrder.Find(item => item.TransportOrderId == transportOrderId && item.TransportService == typeTransportService).FirstOrDefault();
    }

    public List<Order> FindByOrderIds(List<string> orderIds)
    {
        var filter = Builders<Order>.Filter.In(x => x.OrderId, orderIds);
        return _collectionOrder.Find(filter).ToList();
    }

    public List<Order> FindListByPartnerId(string partnerId)
    {
        return _collectionOrder.Find(item => item.PartnerId == partnerId).ToList();
    }

    public PagingResult<Order> ListOrder(Paging paging, string? partnerId = null, string? shopId = null, TypeStatus? status = null)
    {
        PagingResult<Order> result = new PagingResult<Order>();
        FilterDefinition<Order> filterBuilders = Builders<Order>.Filter.And(
            Builders<Order>.Filter.Where(p => partnerId == null || p.PartnerId == partnerId),
            Builders<Order>.Filter.Where(p => shopId == null || p.ShopId == shopId),
            Builders<Order>.Filter.Where(p => status == null || p.Status == status),
            Builders<Order>.Filter.Or(
                Builders<Order>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
                Builders<Order>.Filter.Regex(x => x.TransactionId, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i"))
            )
        );

        var query = _collectionOrder.Find(filterBuilders);
        result.Total = query.ToList().Count;
        result.Result = query.Sort($"{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}").Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public List<Order> FindAll(TypeStatus? status = null)
    {
        return _collectionOrder.Find(item => (status == null || item.Status == status)).SortByDescending(item => item.Updated).ToList();
    }

    public Order? UpdateOrder(Order item)
    {
        Order _item = _collectionOrder.Find(x => x.OrderId == item.OrderId).FirstOrDefault();
        if (_item == null) return null;

        var update = Builders<Order>.Update
            .Set("OrderId", item.OrderId)
            .Set("OrderNo", item.OrderNo)
            .Set("TransportId", item.TransportId)
            .Set("PaymentId", item.PaymentId)
            .Set("TransactionId", item.TransactionId)
            .Set("PartnerId", item.PartnerId)
            .Set("UserShippingAddress", item.UserShippingAddress)
            .Set("ShopId", item.ShopId)
            .Set("ShopName", item.ShopName)
            .Set("ShopProvinceId", item.ShopProvinceId)
            .Set("ShopProvinceName", item.ShopProvinceName)
            .Set("ShopDistrictId", item.ShopDistrictId)
            .Set("ShopDistrictName", item.ShopDistrictName)
            .Set("ShopWardId", item.ShopWardId)
            .Set("ShopWardName", item.ShopWardName)
            .Set("ShopAddress", item.ShopAddress)
            .Set("Notes", item.Notes)
            .Set("OrderOrigin", item.OrderOrigin)
            .Set("ListItems", item.ListItems)
            .Set("VoucherPromotionIds", item.VoucherPromotionIds)
            .Set("VoucherPromotion", item.VoucherPromotion)
            .Set("VoucherTransportIds", item.VoucherTransportIds)
            .Set("VoucherTransport", item.VoucherTransport)
            .Set("Price", item.Price)
            .Set("ExchangePoints", item.ExchangePoints)
            .Set("PointPrice", item.PointPrice)
            .Set("VoucherPromotionPrice", item.VoucherPromotionPrice)
            .Set("VoucherTransportPrice", item.VoucherTransportPrice)
            .Set("TransportPrice", item.TransportPrice)
            .Set("TransportService", item.TransportService)
            .Set("TotalAfterTax", item.TotalAfterTax)
            .Set("StatusTransport", item.StatusTransport)
            .Set("StatusDelivery", item.StatusDelivery)
            .Set("StatusOrder", item.StatusOrder)
            .Set("TypePay", item.TypePay)
            .Set("StatusPay", item.StatusPay)
            .Set("StatusServe", item.StatusServe)
            .Set("TransportOrderId", item.TransportOrderId)
            .Set("Status", item.Status)
            .Set("CompletedAt", item.CompletedAt)
            .Set("TransportOrderLabel", item.TransportOrderLabel)
            .Set("CommissionBreakUp", item.CommissionBreakUp)
            .Set("ExternalId", item.ExternalId)
            .Set("ExternalSource", item.ExternalSource)
            .Set("Created", _item.Created)
            .Set("Updated", DateTimes.Now());
        var filter = Builders<Order>.Filter.Eq("Id", _item.Id);
        var options = new FindOneAndUpdateOptions<Order> { IsUpsert = true, ReturnDocument = ReturnDocument.After };
        return _collectionOrder.FindOneAndUpdate(filter, update, options);
    }

    public long TotalOrder()
    {
        FilterDefinition<Order> filterBuilders = Builders<Order>.Filter.And(
            Builders<Order>.Filter.Where(p => p.OrderId == null)
        );
        return _collectionOrder.CountDocuments(filterBuilders);
    }

    public long TotalOrderFilter(string? shopId = null, TypeOrderStatus[]? statusOrder = null, DateTime? from = null, DateTime? to = null)
    {
        FilterDefinition<Order> filterBuilders = Builders<Order>.Filter.And(
            Builders<Order>.Filter.Where(p => (p.Status == TypeStatus.Actived && (shopId == null || p.ShopId == shopId))),
            Builders<Order>.Filter.Or(
                Builders<Order>.Filter.Where(x => statusOrder == null),
                Builders<Order>.Filter.In(x => x.StatusOrder, statusOrder ?? new TypeOrderStatus[] { })//Mongo required list not empty
            ),
            Builders<Order>.Filter.Or(
                Builders<Order>.Filter.Where(item => from == null),
                Builders<Order>.Filter.Gte(x => x.Created, from)
            ),
            Builders<Order>.Filter.Or(
                Builders<Order>.Filter.Where(item => to == null),
                Builders<Order>.Filter.Lt(x => x.Created, to)
            )
        );
        return _collectionOrder.CountDocuments(filterBuilders);
    }

    // More
    public List<Order> CreateListOrder(List<Order> orders)
    {
        foreach (Order order in orders)
        {
            ObjectId objectId = ObjectId.GenerateNewId();
            order.Id = new BsonObjectId(objectId).ToString();
            order.OrderId = Guid.NewGuid().ToString();
            order.OrderNo = Id64.Generator();
            order.Created = DateTimes.Now();
            order.Updated = DateTimes.Now();
        }
        _collectionOrder.InsertMany(orders);
        return orders;
    }

    public List<Order> FindListByTransactionId(string transactionId)
    {
        return _collectionOrder.Find(item => item.TransactionId == transactionId).ToList();
    }

    public bool UpdateStatusListOrder(List<Order> listOrder, TypeOrderStatus statusOrder)
    {
        try
        {
            if (listOrder.Count > 0)
            {
                var a = _collectionOrder.UpdateMany(x =>
                        x.TransactionId == listOrder[0].TransactionId,
                    Builders<Order>.Update.Set(p => p.StatusOrder, statusOrder),
                    new UpdateOptions { IsUpsert = false }
                );
            }
            return true;
        }
        catch (Exception ex)
        {
            Logs.debug($"[OrderRepository] Exception: {ex.StackTrace}");
            return false;
        }
    }

    private TypeOrderStatus MapStatusOrder(ListOrderStatusEnum status)
    {
        return status switch
        {
            ListOrderStatusEnum.Pending => TypeOrderStatus.Pending,
            // ListOrderStatus.Paid => TypeOrderStatus.Paid,
            ListOrderStatusEnum.Success => TypeOrderStatus.Success,
            ListOrderStatusEnum.Failed => TypeOrderStatus.Failed,
            ListOrderStatusEnum.Refund => TypeOrderStatus.Refund,
            _ => throw new ArgumentException("Invalid status"),
        };
    }

    private TypeTransportStatus? MapTransportStatus(TypeListOrderTransportStatusEnum transportStatus)
    {
        return transportStatus switch
        {

            TypeListOrderTransportStatusEnum.Created => TypeTransportStatus.Created,
            TypeListOrderTransportStatusEnum.Verified => TypeTransportStatus.Verified,
            TypeListOrderTransportStatusEnum.WaitingForDelivery => TypeTransportStatus.WaitingForDelivery,
            TypeListOrderTransportStatusEnum.Delivered => TypeTransportStatus.Delivered,
            TypeListOrderTransportStatusEnum.Transporting => TypeTransportStatus.Transporting,
            TypeListOrderTransportStatusEnum.Delivering => TypeTransportStatus.Delivering,
            TypeListOrderTransportStatusEnum.Success => TypeTransportStatus.Success,
            TypeListOrderTransportStatusEnum.Waiting => TypeTransportStatus.Waiting,
            TypeListOrderTransportStatusEnum.Refunding => TypeTransportStatus.Refunding,
            TypeListOrderTransportStatusEnum.Refunded => TypeTransportStatus.Refunded,
            TypeListOrderTransportStatusEnum.Cancel => TypeTransportStatus.Cancel,
            _ => throw new ArgumentException("Invalid status"),
        };
    }

    // Hàm build filter chung từ ListOrderInputDto
    private List<FilterDefinition<Order>> BuildOrderFilterFromInputDto(ListOrderInputDto objFilter)
    {
        var filters = new List<FilterDefinition<Order>>();
        filters.Add(Builders<Order>.Filter.Where(p => p.ShopId == objFilter.ShopId));
        filters.Add(Builders<Order>.Filter.Where(p => p.Status == TypeStatus.Actived));
        if (!string.IsNullOrEmpty(objFilter.BranchId))
            filters.Add(Builders<Order>.Filter.Where(p => p.BranchId == objFilter.BranchId));
        if (!string.IsNullOrEmpty(objFilter.UserId))
            filters.Add(Builders<Order>.Filter.Where(p => p.Creator.UserId == objFilter.UserId));
        if (objFilter.ItemsType.HasValue)
            filters.Add(Builders<Order>.Filter.ElemMatch(p => p.ListItems, item => item.ItemsType == objFilter.ItemsType.Value));
        if (objFilter.OrderSource.HasValue)
        {
            if (objFilter.OrderSource == TypeListOrderSourceEnum.Partner)
                filters.Add(Builders<Order>.Filter.Ne(p => p.PartnerId, null));
            else if (objFilter.OrderSource == TypeListOrderSourceEnum.User)
                filters.Add(Builders<Order>.Filter.Eq(p => p.PartnerId, null));
        }
        if (objFilter.OrderOrigin.HasValue)
            filters.Add(Builders<Order>.Filter.Where(p => p.OrderOrigin == objFilter.OrderOrigin));
        if (objFilter.FromDate.HasValue)
            filters.Add(Builders<Order>.Filter.Gte(p => p.Created, objFilter.FromDate.Value.Date));
        if (objFilter.ToDate.HasValue)
            filters.Add(Builders<Order>.Filter.Lt(p => p.Created, objFilter.ToDate.Value.Date.AddDays(1)));

        if (!string.IsNullOrEmpty(objFilter.Search))
        {
            string searchQuery = objFilter.Search.Trim().EscapeSpecialChars();

            filters.Add(Builders<Order>.Filter.Or(
                Builders<Order>.Filter.Regex(x => x.Creator.FullName, new BsonRegularExpression(searchQuery, "i")),
                Builders<Order>.Filter.Regex(x => x.Creator.PhoneNumber, new BsonRegularExpression(searchQuery, "i")),
                Builders<Order>.Filter.Regex(x => x.OrderNo, new BsonRegularExpression(searchQuery, "i")),
                Builders<Order>.Filter.ElemMatch(x => x.ListItems, item =>
                    item.ItemsName.ToLower().Contains(searchQuery.ToLower()))
            ));
        }

        return filters;
    }

    public async Task<PagingResult<Order>> ListOrderExtend(Paging paging, ListOrderInputDto objFilter)
    {
        Console.WriteLine($"ListOrderExtend ListOrderInputDto: {JsonConvert.SerializeObject(objFilter)}");
        PagingResult<Order> result = new PagingResult<Order>();

        var filters = BuildOrderFilterFromInputDto(objFilter);

        if (objFilter.StatusOrder != ListOrderStatusEnum.All)
        {
            if (objFilter.StatusOrder == ListOrderStatusEnum.WaitingForDelivery)
            {
                filters.Add(Builders<Order>.Filter.And(
                    Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
                    Builders<Order>.Filter.Where(p => p.StatusTransport == TypeTransportStatus.WaitingForDelivery)
                ));
            }
            else if (objFilter.StatusOrder == ListOrderStatusEnum.Delivering)
            {
                filters.Add(Builders<Order>.Filter.And(
                    Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
                    Builders<Order>.Filter.Where(p => p.StatusTransport == TypeTransportStatus.Delivering)
                ));
            }
            else if (objFilter.StatusOrder == ListOrderStatusEnum.Pending)
            {
                filters.Add(Builders<Order>.Filter.And(
                    Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
                    Builders<Order>.Filter.Where(p => p.StatusTransport == TypeTransportStatus.Created)
                ));
            }
            else
            {
                var mappedStatus = MapStatusOrder(objFilter.StatusOrder);
                filters.Add(Builders<Order>.Filter.Where(p => p.StatusOrder == mappedStatus));
            }
        }

        if (objFilter.TransportStatus != TypeListOrderTransportStatusEnum.All)
        {
            var mappedTransportStatus = MapTransportStatus(objFilter.TransportStatus);
            filters.Add(Builders<Order>.Filter.Where(p => p.StatusTransport == mappedTransportStatus));
        }

        FilterDefinition<Order> filterBuilders = Builders<Order>.Filter.And(filters);

        var options = new FindOptions<Order>
        {
            Skip = (paging.PageIndex * paging.PageSize),
            Limit = paging.PageSize,
            Sort = Builders<Order>.Sort.Descending(x => x.Created)
        };

        var totalCount = await _collectionOrder.CountDocumentsAsync(filterBuilders);

        var list = await _collectionOrder.FindAsync(filterBuilders, options);
        var items = await list.ToListAsync();

        return new PagingResult<Order>
        {
            Total = (int)totalCount,
            Result = items
        };
    }

    public PagingResult<Order> ListOrderUser(Paging paging, string userId, TypeItems? typeItems, ListOrderStatusEnum statusOrder = ListOrderStatusEnum.All)
    {
        PagingResult<Order> result = new PagingResult<Order>();

        // Xây dựng bộ lọc
        var filters = new List<FilterDefinition<Order>>
        {
            Builders<Order>.Filter.Where(p => p.Creator.UserId == userId),

            // Lọc trạng thái "Actived"
            Builders<Order>.Filter.Where(p => p.Status == TypeStatus.Actived)
        };

        // Lọc theo StatusOrder nếu không phải "All"
        if (statusOrder != ListOrderStatusEnum.All)
        {
            if (statusOrder == ListOrderStatusEnum.WaitingForDelivery)
            {
                var condition1 = Builders<Order>.Filter.And(
                    Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
                    Builders<Order>.Filter.Where(p => p.StatusTransport == TypeTransportStatus.WaitingForDelivery)
                );

                var condition2 = Builders<Order>.Filter.And(
                    Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
                    Builders<Order>.Filter.Where(p => p.StatusPay == TypePayStatus.Paid)
                );

                filters.Add(Builders<Order>.Filter.Or(condition1, condition2));
            }
            else if (statusOrder == ListOrderStatusEnum.Delivering)
            {
                filters.Add(Builders<Order>.Filter.And(
            Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
            Builders<Order>.Filter.Where(p => p.StatusTransport == TypeTransportStatus.Delivering)
            ));

            }
            else if (statusOrder == ListOrderStatusEnum.Pending)
            {
                filters.Add(Builders<Order>.Filter.And(
            Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
            Builders<Order>.Filter.Where(p => p.StatusTransport == TypeTransportStatus.Created)
            ));

            }
            else
            {
                var mappedStatus = MapStatusOrder(statusOrder); // Chuyển đổi status
                filters.Add(Builders<Order>.Filter.Where(p => p.StatusOrder == mappedStatus));
            }

        }

        // Lọc theo itemsType trong listItems (nếu được chỉ định)
        if (typeItems.HasValue)
        {
            filters.Add(Builders<Order>.Filter.ElemMatch(p => p.ListItems, item => item.ItemsType == typeItems.Value));
        }

        // Lọc theo tìm kiếm (nếu có)
        filters.Add(Builders<Order>.Filter.Or(
            Builders<Order>.Filter.Where(x => string.IsNullOrEmpty(paging.Search)),
            Builders<Order>.Filter.Regex(x => x.UserShippingAddress.FullName, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i")),
            Builders<Order>.Filter.Regex(x => x.UserShippingAddress.PhoneNumber, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i")),
             Builders<Order>.Filter.Regex(x => x.OrderNo, new BsonRegularExpression($@"{paging.Search}".EscapeSpecialChars(), "i")),
            Builders<Order>.Filter.ElemMatch(x => x.ListItems, item =>
                item.ItemsName.ToLower().Contains(paging.Search.ToLower())) // Tìm kiếm trong listItems
        ));

        // Kết hợp các bộ lọc với AND
        FilterDefinition<Order> filterBuilders = Builders<Order>.Filter.And(filters);

        // Thực hiện truy vấn
        var query = _collectionOrder.Find(filterBuilders);
        result.Total = query.ToList().Count;
        result.Result = query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}").Skip(paging.PageIndex * paging.PageSize).Limit(paging.PageSize).ToList();
        return result;
    }

    public async Task<long> TotalPriceOrderByUserId(string userId, TypePointCalculation typePointCalculation)
    {
        var filterBuilder = Builders<Order>.Filter;
        var filter = filterBuilder.Eq(o => o.Creator.UserId, userId);

        filter &= filterBuilder.Eq(o => o.StatusOrder, TypeOrderStatus.Success);

        // Lấy danh sách đơn hàng của người dùng với điều kiện lọc
        var orders = await _collectionOrder.Find(filter).ToListAsync();


        // Kiểm tra nếu không có đơn hàng
        if (orders == null || !orders.Any())
        {
            return 0; // Trả về 0 nếu không có đơn hàng
        }

        // Tính tổng giá trị đơn hàng dựa trên phương thức tính điểm
        if (typePointCalculation == TypePointCalculation.total_order_value)
        {
            return (long)orders.Sum(o => o.Price); // Tổng giá trị thanh toán đơn hàng
        }
        else if (typePointCalculation == TypePointCalculation.total_excluding_shipping)
        {
            return (long)orders.Sum(o => o.VoucherTransportPrice > 0 ? o.Price : o.Price - o.TransportPrice); // Tổng giá trị thanh toán đơn hàng

        }

        return 0; // Trả về 0 nếu không có đơn hàng hoặc phương thức không hợp lệ
    }

    public async Task<decimal> GetTotalSpentByUserId(string userId)
    {
        var filterBuilder = Builders<Order>.Filter;
        var filter = filterBuilder.Eq(o => o.Creator.UserId, userId);

        filter &= filterBuilder.Eq(o => o.StatusPay, TypePayStatus.Paid);

        // Lấy danh sách đơn hàng đã thanh toán của người dùng
        var orders = await _collectionOrder.Find(filter).ToListAsync();

        // Kiểm tra nếu không có đơn hàng
        if (orders == null || !orders.Any())
        {
            return 0; // Trả về 0 nếu không có đơn hàng
        }

        // Tính tổng giá trị TotalAfterTax của tất cả đơn hàng đã thanh toán
        return orders.Sum(o => o.TotalAfterTax);
    }
    public Order? FindByOrderThirdParty(string orderId)
    {
        return _collectionOrder.Find(item => item.ExternalId == orderId).FirstOrDefault();
    }
    public async Task<PagingResult<Order>> GetOrdersAsync(List<FilterDefinition<Order>> filters, PagingConfig pagingInfo)
    {
        FilterDefinition<Order> filterBuilders = Builders<Order>.Filter.And(filters);
        var result = new PagingResult<Order> { };
        // Define sort by Created in descending order (newest first)
        var sortDefinition = Builders<Order>.Sort.Descending(o => o.Created);
        // Get total records
        var totalRecords = await _collectionOrder.CountDocumentsAsync(filterBuilders);
        result.Total = totalRecords;

        if (pagingInfo.SkipedPaging)
        {
            var ordersNoPaging = await _collectionOrder.Find(filterBuilders).Sort(sortDefinition).ToListAsync();
            result.Result = ordersNoPaging;
            return result;

        }
        // Get orders with pagination
        var orders = await _collectionOrder.Find(filterBuilders)
                                  .Skip((pagingInfo.PageIndex - 1) * pagingInfo.PageSize)
                                  .Limit(pagingInfo.PageSize)
                                  .Sort(sortDefinition)
                                  .ToListAsync();

        result.Result = orders;
        return result;
    }

    public async Task<List<Order>> GetOrdersByFilter(FilterDefinition<Order> filter)
    {
        return await _collectionOrder.Find(filter).ToListAsync();
    }

    public List<Order> GetUniqueSuccessOrdersByCreatorId(List<string> userIds)
    {
        // Tạo bộ lọc để tìm các đơn hàng có trạng thái Success và CreatorId nằm trong danh sách userIds
        var filter = Builders<Order>.Filter.And(
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Success),
            Builders<Order>.Filter.In(x => x.Creator.UserId, userIds)
        );

        // Lấy danh sách các đơn hàng thỏa mãn điều kiện lọc
        var successOrders = _collectionOrder.Find(filter).ToList();

        // Nhóm các đơn hàng theo CreatorId và lấy đơn hàng đầu tiên trong mỗi nhóm
        var uniqueOrders = successOrders
            .GroupBy(order => order.Creator.UserId)
            .Select(group => group.First())
            .ToList();

        return uniqueOrders;
    }

    public async Task<PagingResult<Order>> ListOrderSuccess(Paging paging, string f0UserId, List<string> subUserIds, DateTime? start = null)
    {
        PagingResult<Order> result = new PagingResult<Order>();

        // Tạo list filter
        var filterList = new List<FilterDefinition<Order>>
        {
            Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Success),
            Builders<Order>.Filter.ElemMatch<CommissionDistribution>(o => o.CommissionBreakUp.CommissionDistribution,
                Builders<CommissionDistribution>.Filter.And(
                    Builders<CommissionDistribution>.Filter.Eq(cd => cd.UserId, f0UserId),
                    Builders<CommissionDistribution>.Filter.Eq(cd => cd.IsActive, true)
                ))
        };

        // Nếu có ngày bắt đầu, thêm điều kiện CreatedAt >= start
        if (start.HasValue)
        {
            filterList.Add(Builders<Order>.Filter.Gte(o => o.Created, start.Value));
        }

        var ordersFilter = Builders<Order>.Filter.And(filterList);

        var query = _collectionOrder.Find(ordersFilter);

        result.Total = await query.CountDocumentsAsync();

        result.Result = await query.Sort($"{{{paging.NameType}: {(paging.SortType == TypeSort.asc ? 1 : -1)}}}")
                                   .Skip(paging.PageIndex * paging.PageSize)
                                   .Limit(paging.PageSize)
                                   .ToListAsync();

        return result;
    }

    public async Task<List<Order>> GetUnpaidOrdersOlderThanMinutes(int minutes)
    {
        var cutoffTime = DateTime.Now.AddMinutes(-minutes);

        var filter = Builders<Order>.Filter.And(
            Builders<Order>.Filter.Nin(o => o.TypePay, new[] { TypePayment.COD, TypePayment.Other }),
            Builders<Order>.Filter.Eq(o => o.StatusPay, TypePayStatus.NotPaid),
            Builders<Order>.Filter.Lt(o => o.Created, cutoffTime),
            Builders<Order>.Filter.Eq(o => o.StatusOrder, TypeOrderStatus.Pending)
        );

        return await _collectionOrder.Find(filter).ToListAsync();
    }

    public async Task<byte[]> ExportListOrder(List<OrderPartnerDto> list)
    {
        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Sheet1");

        // Định nghĩa các header
        var headers = ExportConst.HEADER_EXPORT_ORDER;

        // Tạo header
        for (int i = 0; i < headers.Length; i++)
        {
            worksheet.Cells[1, i + 1].Value = headers[i];
        }

        // Style cho header
        var headerRange = worksheet.Cells[1, 1, 1, headers.Length];
        headerRange.Style.Font.Bold = true;
        headerRange.Style.Font.Name = "Times New Roman";
        headerRange.Style.Font.Size = 13;
        headerRange.Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        headerRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
        headerRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
        headerRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
        headerRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;

        // Nếu không có dữ liệu, trả về file với header
        if (list == null || list.Count == 0)
        {
            for (int i = 1; i <= headers.Length; i++)
            {
                worksheet.Column(i).Width = 35;
            }

            return package.GetAsByteArray();
        }

        int row = 2; // Bắt đầu từ dòng 2
        int stt = 1;

        foreach (var order in list)
        {
            var listItems = order.ListItems;

            foreach (var item in listItems)
            {
                worksheet.Cells[row, 1].Value = stt++;                              // STT
                worksheet.Cells[row, 2].Value = order?.OrderNo ?? "";               // Mã đơn hàng
                worksheet.Cells[row, 3].Value = item?.ItemsName ?? "";              // Tên sản phẩm
                worksheet.Cells[row, 4].Value = item?.Quantity;                     // Số lượng
                worksheet.Cells[row, 5].Value = item?.Price;                        // Đơn giá
                worksheet.Cells[row, 6].Value = order?.Created;                     // Ngày tạo đơn
                worksheet.Cells[row, 6].Style.Numberformat.Format = "dd/MM/yyyy";
                worksheet.Cells[row, 7].Value = order?.Creator?.FullName;          // Tên khách hàng
                worksheet.Cells[row, 8].Value = order?.Creator?.PhoneNumber.FormatPhonePrefix0(); // SĐT khách hàng
                worksheet.Cells[row, 9].Value = order.Price;                       // Tổng tiền đơn hàng
                worksheet.Cells[row, 10].Value = GetTransportStatusLabel(order?.StatusTransport);// Trạng thái vận chuyển
                worksheet.Cells[row, 11].Value = GetPayStatusLabel(order?.StatusPay);// Trạng thái thanh toán
                worksheet.Cells[row, 12].Value = GetTransportServiceLabel(order.StatusDelivery, order.TransportService);           // Phương thức giao hàng
                worksheet.Cells[row, 13].Value = order?.TransportOrderId ?? "";           // Mã vận chuyển
                worksheet.Cells[row, 14].Value = "";        // Nhãn vận chuyển
                row++;
            }
        }

        // Style toàn bộ bảng (header + dữ liệu)
        var dataRange = worksheet.Cells[1, 1, row - 1, headers.Length];
        dataRange.Style.Font.Name = "Times New Roman";
        dataRange.Style.Font.Size = 13;
        dataRange.Style.Border.Top.Style = ExcelBorderStyle.Thin;
        dataRange.Style.Border.Bottom.Style = ExcelBorderStyle.Thin;
        dataRange.Style.Border.Left.Style = ExcelBorderStyle.Thin;
        dataRange.Style.Border.Right.Style = ExcelBorderStyle.Thin;

        // Căn lề một số cột
        worksheet.Column(1).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // STT
        worksheet.Column(2).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Mã đơn hàng
        worksheet.Column(3).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;   // Tên sản phẩm
        worksheet.Column(4).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // SL
        worksheet.Column(5).Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;  // Đơn giá
        worksheet.Column(6).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // Ngày tạo
        worksheet.Column(7).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;   // Tên KH
        worksheet.Column(8).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center; // SĐT KH
        worksheet.Column(9).Style.HorizontalAlignment = ExcelHorizontalAlignment.Right;  // Tổng tiền
        worksheet.Column(10).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        worksheet.Column(11).Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
        worksheet.Column(12).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
        worksheet.Column(13).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;
        worksheet.Column(14).Style.HorizontalAlignment = ExcelHorizontalAlignment.Left;

        for (int i = 1; i <= headers.Length; i++)
        {
            worksheet.Column(i).Width = 35;
        }

        return package.GetAsByteArray();
    }

    private string GetTransportServiceLabel(TypeDelivery statusDelivery, TypeTransportService? transportService)
    {
        return statusDelivery switch
        {
            TypeDelivery.InShop => "Nhận hàng tại quầy",
            TypeDelivery.ExpressDelivery => transportService switch
            {
                TypeTransportService.LCOD => "Shop tự giao hàng",
                TypeTransportService.AHAMOVE => "Ahamove",
                _ => "Trạng thái không xác định"
            },
            _ => "Trạng thái không xác định"
        };
    }

    private string GetTransportStatusLabel(TypeTransportStatus? status)
    {
        return status switch
        {
            TypeTransportStatus.Created => "Chờ vận chuyển",
            TypeTransportStatus.WaitingForDelivery => "Chờ vận chuyển",
            TypeTransportStatus.Delivering => "Đang vận chuyển",
            TypeTransportStatus.Refunded => "Đã trả hàng",
            TypeTransportStatus.Success => "Hoàn thành",
            TypeTransportStatus.Cancel => "Đã hủy",
            _ => "Trạng thái không xác định"
        };
    }

    private string GetPayStatusLabel(TypePayStatus? status)
    {
        return status switch
        {
            TypePayStatus.NotPaid => "Chưa thanh toán",
            TypePayStatus.Paid => "Đã thanh toán",
            TypePayStatus.Refund => "Đã hoàn tiền",
            _ => "Trạng thái không xác định"
        };
    }

    public async Task<TotalOrderStatusDto> GetTotalOrderStatus(string shopId, string userId)
    {
        var result = new TotalOrderStatusDto();

        // Tạo filter cơ bản cho shopId và userId
        var filters = new List<FilterDefinition<Order>>
        {
            Builders<Order>.Filter.Eq(x => x.ShopId, shopId),
            Builders<Order>.Filter.Eq(x => x.Creator.UserId, userId),
            Builders<Order>.Filter.Eq(x => x.Status, TypeStatus.Actived)
        };

        // Đếm tổng số đơn hàng
        var allFilter = Builders<Order>.Filter.And(filters);
        result.All = (int)await _collectionOrder.CountDocumentsAsync(allFilter);

        // Đếm số đơn hàng theo trạng thái đơn hàng (StatusOrder)
        var pendingFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Pending),
            Builders<Order>.Filter.Eq(x => x.StatusTransport, TypeTransportStatus.Created)
        };
        result.Pendding = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(pendingFilters));

        // Đếm số đơn hàng có trạng thái WaitingForDelivery
        var waitingForDeliveryFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Pending),
            Builders<Order>.Filter.Or(
                Builders<Order>.Filter.Eq(x => x.StatusTransport, TypeTransportStatus.WaitingForDelivery),
                Builders<Order>.Filter.Eq(x => x.StatusPay, TypePayStatus.Paid)
            )
        };
        result.WaitingForDelivery = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(waitingForDeliveryFilters));

        // Đếm số đơn hàng có trạng thái Delivering
        var deliveringFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Pending),
            Builders<Order>.Filter.Eq(x => x.StatusTransport, TypeTransportStatus.Delivering)
        };
        result.Delivering = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(deliveringFilters));

        // Đếm số đơn hàng có trạng thái Success
        var successFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Success)
        };
        result.Success = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(successFilters));

        // Đếm số đơn hàng có trạng thái Failed
        var failedFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Failed)
        };
        result.Failed = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(failedFilters));

        // Đếm số đơn hàng có trạng thái Refund
        var refundFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Refund)
        };
        result.Refund = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(refundFilters));

        return result;
    }

    public async Task<TotalOrderStatusDto> GetTotalOrderStatusByFilter(ListOrderInputDto objFilter)
    {
        var result = new TotalOrderStatusDto();

        var filters = BuildOrderFilterFromInputDto(objFilter);

        // Đếm tổng số đơn (All)
        result.All = (long)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(filters));

        // Pending
        var pendingFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Pending),
            Builders<Order>.Filter.Eq(x => x.StatusTransport, TypeTransportStatus.Created)
        };
        result.Pendding = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(pendingFilters));

        // WaitingForDelivery
        var waitingForDeliveryFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Where(p => p.StatusOrder == TypeOrderStatus.Pending),
            Builders<Order>.Filter.Where(p => p.StatusTransport == TypeTransportStatus.WaitingForDelivery)
        };
        result.WaitingForDelivery = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(waitingForDeliveryFilters));

        // Delivering
        var deliveringFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Pending),
            Builders<Order>.Filter.Eq(x => x.StatusTransport, TypeTransportStatus.Delivering)
        };
        result.Delivering = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(deliveringFilters));

        // Success
        var successFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Success)
        };
        result.Success = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(successFilters));

        // Failed
        var failedFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Failed)
        };
        result.Failed = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(failedFilters));

        // Refund
        var refundFilters = new List<FilterDefinition<Order>>(filters)
        {
            Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Refund)
        };
        result.Refund = (int)await _collectionOrder.CountDocumentsAsync(Builders<Order>.Filter.And(refundFilters));

        return result;
    }

    public int GetTotalQuantityPurchased(string shopId, string userId, string itemsId)
    {
        var filter = Builders<Order>.Filter.And(
            Builders<Order>.Filter.Eq(x => x.ShopId, shopId),
            Builders<Order>.Filter.Eq(x => x.Creator.UserId, userId),
            // Builders<Order>.Filter.Eq(x => x.StatusOrder, TypeOrderStatus.Success),
            Builders<Order>.Filter.Eq(x => x.Status, TypeStatus.Actived),
            Builders<Order>.Filter.Nin(x => x.StatusOrder, new[] { TypeOrderStatus.Refund, TypeOrderStatus.Failed }),
            Builders<Order>.Filter.ElemMatch(x => x.ListItems, item => item.ItemsId == itemsId)
        );

        var orders = _collectionOrder.Find(filter).ToList();

        int totalQuantity = 0;
        foreach (var order in orders)
        {
            var matchingItems = order.ListItems.Where(item => item.ItemsId == itemsId);
            totalQuantity += matchingItems.Sum(item => item.Quantity ?? 0);
        }

        return totalQuantity;
    }
}